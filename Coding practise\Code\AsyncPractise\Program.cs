﻿
//Console.WriteLine("Hello world!");
//TestAsync();
//Console.WriteLine("Async execution Started");
//Console.ReadLine();


//async Task TestAsync()
//{
//    var client = new HttpClient();
//    var response = await client.GetAsync("https://google.com");
//    Console.WriteLine(response.StatusCode);
//}


//static void Main(string[] args)
//{
//    //Thread T1 = new Thread(() => foo(1));
//    //Thread T2 = new Thread(() => foo(2));

//    //T1.Start();
//    //T2.Start();
//    Console.WriteLine("Main thread started at {0}", DateTime.Now);
//}

//static void foo(int i)
//{
//   Task.Delay(10000).Wait(); // Simulating some work with a delay
//    Console.WriteLine("Thread {0} finished work at {1}", Thread.CurrentThread.ManagedThreadId, DateTime.Now);
//}

public class TreeNode
{
    public int Value;
    public TreeNode? Left;
    public TreeNode? Right;
}

public class BinaryTree
{
    public int GetDepth(TreeNode? root)
    {
        if (root == null)
            return 0;
        int leftDepth = GetDepth(root.Left);
        int rightDepth = GetDepth(root.Right);
        return Math.Max(leftDepth, rightDepth) + 1;
    }

   
}
public class Program
{
    public static void Main(string[] args)
    {
        // Constructing the binary tree:
        //       1
        //      / \
        //     2   3
        //    /
        //   4
        //../
        // 9

        TreeNode root = new TreeNode
        {
            Value = 1,
            Left = new TreeNode
            {
                Value = 2,
                Left = new TreeNode { Value = 4, Left = new TreeNode { Value = 9 } }
            },
            Right = new TreeNode { Value = 3 }
        };

        BinaryTree tree = new BinaryTree();
        int depth = tree.GetDepth(root);

        Console.WriteLine("Depth of the tree: " + depth); // Output: 4
    }
}// This code defines a binary tree and calculates its depth using recursion.



