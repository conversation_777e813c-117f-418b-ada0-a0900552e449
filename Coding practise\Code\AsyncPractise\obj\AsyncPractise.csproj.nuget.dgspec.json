{"format": 1, "restore": {"D:\\Interview\\Coding practise\\Code\\AsyncPractise\\AsyncPractise.csproj": {}}, "projects": {"D:\\Interview\\Coding practise\\Code\\AsyncPractise\\AsyncPractise.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Interview\\Coding practise\\Code\\AsyncPractise\\AsyncPractise.csproj", "projectName": "AsyncPractise", "projectPath": "D:\\Interview\\Coding practise\\Code\\AsyncPractise\\AsyncPractise.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Interview\\Coding practise\\Code\\AsyncPractise\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Software Installation\\Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://pkgs.dev.azure.com/office/DigiTS/_packaging/DigiTS/nuget/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}