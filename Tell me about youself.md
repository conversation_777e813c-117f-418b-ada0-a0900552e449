**======================================================================= TECHNICAL =================================================================================================**



**Tell me about youself**



I'm a .NET developer with 15 years of experience, currently working as a Technology Lead at Infosys. I have experience in building and developing enterprise-grade cloud native applications using C#, ASP.NET Core, Web API, and SQL Server - technologies that are central to this role.



At Infosys, I've been involved in building scalable microservices and RESTful APIs(Eg:Microsoft's Digital Trust and Safety platform). I've also worked extensively on systems like Changepoint for labor management and various enterprise services platforms. My experience spans from legacy .NET Framework modernization to cloud-native solutions on Azure.



**\*\*What challenges/problems have you faced or solved in your recent roles and how did you address them? Could you give some examples?\*\***

* Memory leaks :\*\* I faced a issue with memory leaks in a high-traffic .NET web application, which led to performance degradation over time. There is a specific workflow in content scanning where data is loaded in memory for scanning.As the data grew in time, the amount of data held in memory was causing memory issues leading to pod crahes and requests timeouts.This was identified by systematically profiling the application, identified improper disposal of unmanaged resources, and refactored the code to implement the IDisposable pattern correctly. This resolved the leaks and stabilized the application’s performance.



**- Package dependency issue :** Dependency I believe I possess a strong aptitude for anomaly detection. For instance, on one occasion, a package dependency issue caused a library responsible for frame extraction in the content scanning module to fail silently in the background. As a result, it was unable to extract frames from videos, which led to numerous content violations being incorrectly marked as safe and caused a significant number of false positives. I identified this issue while working on development in a related area and testing that specific module.



Lessons learnt: Failure due not not having integration tests.Built them in next iteration.Built observability metrics around the service.



  **.Net framework migration:**

       1. Compatibility Issues : Many libraries that work with.NET Framework were not available or fully compatible with.NET Core. Used NET Portability Analyzer, .NET migration assistant. These tools helped identify unsupported APIs and libraries. For unsupported libraries, we refactored the code to eliminate reliance on incompatible components.
       2. Due to the difference in Api, we also rewrote the routing logic for the Odata web api and also DBContext related code(initilaztion etc) had to be rewritten to support the FF->EFCore logic.
       3. Build and Deployment pipelines had to be rewritten.



 **Prevented API abuse:**

     One of the APIs on a micro service was facing availability and performance issues. RCA found out that one of the tenant user of the API was sending bulk requests to APIs at a high volume. Implemented rate limiting in APIM to restore SLA.

** Business reports unavaibility due to APIM **

  One of the reports used by business users to see statistics of an api using Azure APIM stopped working correctly suddenly.I figured out that the reporting service uses analytical component of Azure APIM and it was accidentally turned off by azure support while troubleshotting a related issue. Helped the other team in identifying the root cause and in restoring the reporting capability back.





**How did you improve performance of your application?**



* Introduced caching for frequently accessed data(IMemory cache), reducing DB calls.
* Created azure runbook automation for index rebuilding scripts to reduce framgementation, improved latency for api calls.
* payload size of BI audit events sent throught event hub caused performance bottle nek. Reducedd payload size and reduced unncessary events.
* Reduced Azure SQL DTU consumption that cause API timeouts due to DB locks.Reduced the volume of data that is required for hash validation function.

 



**=======================================================================NON-TECHNICAL=================================================================================================**

**What do you know about Quest**

**Quest provides cloud solution services in the area of security, data management \&transformation,migration**

**Why do you want to change job?**



I've really enjoyed my time at Infosys and the projects I've worked on. After 10+ fruitful years with the company, I'm looking for new challenges where I can apply my experience in a different environment. Fortinet's focus on cybersecurity and the opportunity to work on product entitlement systems represents exactly the kind of technical challenge I'm excited about.



**Why do you want to join Quest?**



Quest is a market leader in cyber security solutions provider and the mission to secure the digital world resonates with me deeply, as I belivee it is very critical to protect businesses and individual's in today's connecting world. I'm excited to grow with Fortinet, contribute to it's success, and make a positive impact in this vital field.



**Can you tell about a time you worked as part of a team to solve a problem?**

In my current project, my team faced a challenge where a critical system outage disrupted operations for one of the end customers of my client, breaching SLA.



We were tasked with diagnosing the issue, restoring the functionality quickly, and implementing measures to prevent future occurences.



I colloboarated with team members to divide responsibilities based on expertise.Focussed on troubleshooting the issue by analyzing the logs,configurations while others worked on workarounds to minimize the impact. I also coordinated with the client keping them informed of our progress and managing expectations.



Within 4 hours we restored the functionality, well ahead of the defined SLA time. Client commended us for the transparency and quick resolution.



**Do you have any questions**

* Are there any new technologies or skills expected to be introduced beyond those listed in the job description
* Do you expect the main responsibilities for this position to change in the next six months to a year?
* Is this role anticipated to be an individual contributor, or does it involve team leadership?
* Is this role available for employment visa sponsorship?
* What are the next steps in the hiring process after the coding assessment round?



==========================================FORTINET, Canada=======================================================================================================================

what is oauth?

What is TLS .why is it used?

Difference between hashing and encryption?

Different types of indexes? Differences between clustered and non-clustered index?

What is NoTracking in Entity Framework?

How are web apis authenticated, what auth are you using in your web apis?

What are JWT token and what is it constittued of?

What are the different types of attacks that a web application can face? DDOS/CSRF/XXS cross site scripting etc.

How did you measure the performance improvement in % when migrating from .net framework to .NET core?

Why do we need transactions in SQL? What happens to data deletes with and without transactions?

Difference between NVARCHAR AND VARCHAR?

